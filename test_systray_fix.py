#!/usr/bin/env python3
"""
Test script to verify the system tray icon fix works properly.
This script simulates the problematic scenario where an icon path doesn't exist in the theme.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set GTK version before importing anything else
import gi
gi.require_version("Gtk", "3.0")
gi.require_version("GdkPixbuf", "2.0")

from gi.repository import Gtk, GdkPixbuf

def test_icon_loading():
    """Test various icon loading scenarios"""
    print("Testing system tray icon loading fixes...")
    
    # Test 1: Non-existent file path (like the ProtonVPN case)
    print("\n1. Testing non-existent file path:")
    test_path = "/usr/lib/python3.13/site-packages/proton/vpn/app/gtk/assets/icons/state-disconnected.svg"
    print(f"   Path: {test_path}")
    print(f"   Exists: {os.path.exists(test_path)}")
    
    # Test 2: GTK theme icon availability
    print("\n2. Testing GTK theme icons:")
    theme = Gtk.IconTheme.get_default()
    test_icons = [
        "image-missing",
        "dialog-question", 
        "application-x-executable",
        "text-x-generic",
        "folder"
    ]
    
    for icon in test_icons:
        has_icon = theme.has_icon(icon)
        print(f"   {icon}: {'✓' if has_icon else '✗'}")
        
    # Test 3: Create a simple pixbuf (fallback test)
    print("\n3. Testing pixbuf creation:")
    try:
        pixbuf = GdkPixbuf.Pixbuf.new(
            GdkPixbuf.Colorspace.RGB, True, 8, 24, 24
        )
        pixbuf.fill(0x808080ff)
        print("   Pixbuf creation: ✓")
    except Exception as e:
        print(f"   Pixbuf creation: ✗ ({e})")
    
    print("\nTest completed. The patches should handle missing icons gracefully.")

if __name__ == "__main__":
    # Initialize GTK
    Gtk.init(sys.argv)
    test_icon_loading()
