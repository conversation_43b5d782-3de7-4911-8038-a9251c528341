#!/usr/bin/env python3
"""
Test script for enhanced system tray icon handling
"""

import os
import sys
import gi

gi.require_version("Gtk", "3.0")
gi.require_version("GdkPixbuf", "2.0")
from gi.repository import Gtk, GdkPixbuf

def test_icon_scenarios():
    """Test various icon loading scenarios that the enhanced handler should support"""
    print("Testing enhanced system tray icon handling scenarios...")
    
    # Initialize GTK
    Gtk.init(sys.argv)
    
    # Test scenarios
    test_cases = [
        # File path scenarios
        {
            "name": "Absolute SVG path (existing)",
            "icon": "/usr/share/icons/hicolor/scalable/apps/firefox.svg",
            "expected": "Should load if file exists"
        },
        {
            "name": "Absolute SVG path (non-existing)",
            "icon": "/usr/lib/python3.13/site-packages/proton/vpn/app/gtk/assets/icons/state-disconnected.svg",
            "expected": "Should try fallbacks: filename extraction, common locations"
        },
        {
            "name": "Relative path with filename",
            "icon": "assets/icons/app-icon.svg",
            "expected": "Should extract 'app-icon' for theme lookup"
        },
        {
            "name": "Just filename",
            "icon": "firefox.svg",
            "expected": "Should try 'firefox' in theme"
        },
        # Theme icon scenarios
        {
            "name": "Standard theme icon",
            "icon": "firefox",
            "expected": "Should load from theme if available"
        },
        {
            "name": "Generic fallback",
            "icon": "application-x-executable",
            "expected": "Should load generic app icon"
        }
    ]
    
    theme = Gtk.IconTheme.get_default()
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['name']}")
        print(f"   Icon: {case['icon']}")
        print(f"   Expected: {case['expected']}")
        
        # Test file existence
        if os.path.isabs(case['icon']):
            exists = os.path.exists(case['icon'])
            print(f"   File exists: {exists}")
            
            if not exists:
                # Test filename extraction
                filename = os.path.basename(case['icon'])
                name_without_ext = os.path.splitext(filename)[0]
                print(f"   Extracted filename: {filename}")
                print(f"   Name without extension: {name_without_ext}")
                print(f"   Theme has '{name_without_ext}': {theme.has_icon(name_without_ext)}")
                print(f"   Theme has '{filename}': {theme.has_icon(filename)}")
                
                # Test common locations
                common_dirs = [
                    "/usr/share/icons",
                    "/usr/share/pixmaps",
                    "/usr/local/share/icons",
                    "/usr/local/share/pixmaps"
                ]
                
                for icon_dir in common_dirs:
                    potential_path = os.path.join(icon_dir, filename)
                    if os.path.exists(potential_path):
                        print(f"   Found in: {potential_path}")
                        break
        else:
            # Test theme availability
            print(f"   Theme has icon: {theme.has_icon(case['icon'])}")
            
            # If it contains path separators, extract filename
            if '/' in case['icon']:
                filename = os.path.basename(case['icon'])
                name_without_ext = os.path.splitext(filename)[0]
                print(f"   Extracted filename: {filename}")
                print(f"   Name without extension: {name_without_ext}")
                print(f"   Theme has '{name_without_ext}': {theme.has_icon(name_without_ext)}")

def test_pixbuf_loading():
    """Test actual pixbuf loading for different scenarios"""
    print("\n" + "="*60)
    print("Testing actual pixbuf loading...")
    
    # Test loading a common system icon
    theme = Gtk.IconTheme.get_default()
    test_icons = ["firefox", "application-x-executable", "folder", "image-missing"]
    
    for icon in test_icons:
        try:
            if theme.has_icon(icon):
                pixbuf = theme.load_icon(icon, 24, Gtk.IconLookupFlags.FORCE_SIZE)
                if pixbuf:
                    print(f"✓ Successfully loaded '{icon}' ({pixbuf.get_width()}x{pixbuf.get_height()})")
                else:
                    print(f"✗ Failed to load '{icon}' (pixbuf is None)")
            else:
                print(f"✗ Icon '{icon}' not found in theme")
        except Exception as e:
            print(f"✗ Error loading '{icon}': {e}")
    
    # Test loading from file if available
    test_files = [
        "/usr/share/pixmaps/firefox.png",
        "/usr/share/icons/hicolor/48x48/apps/firefox.png"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                pixbuf = GdkPixbuf.Pixbuf.new_from_file_at_size(file_path, 24, 24)
                if pixbuf:
                    print(f"✓ Successfully loaded from file: {file_path}")
                else:
                    print(f"✗ Failed to load from file: {file_path}")
            except Exception as e:
                print(f"✗ Error loading from file {file_path}: {e}")

if __name__ == "__main__":
    test_icon_scenarios()
    test_pixbuf_loading()
    print("\n" + "="*60)
    print("Enhanced icon handling test completed!")
    print("\nThe enhanced system tray icon handler should now:")
    print("1. Try GTK theme icons first (existing behavior)")
    print("2. Check if icon name is a file path and load directly if exists")
    print("3. Extract filename from paths for theme lookup")
    print("4. Search common icon directories for missing files")
    print("5. Handle both SVG and other image formats")
    print("6. Gracefully fallback to original behavior if all else fails")
